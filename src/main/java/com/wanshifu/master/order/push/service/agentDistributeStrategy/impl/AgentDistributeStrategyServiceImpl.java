package com.wanshifu.master.order.push.service.agentDistributeStrategy.impl;

import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.AgentDistributeStrategyApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.enums.AgentDistributeRule;
import com.wanshifu.master.order.push.domain.po.AgentDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.AgentInfo;
import com.wanshifu.master.order.push.domain.request.common.GetServeByServeIdsReq;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.resp.agent.AgentDistributeDetailResp;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.AgentDistributeStrategyDetailResp;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.GetAgentDistributeStrategyListResp;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.GetAgentListResp;
import com.wanshifu.master.order.push.domain.response.common.GetServeByServeIdsResp;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.rqt.agent.*;
import com.wanshifu.master.order.push.service.agentDistributeStrategy.AgentDistributeStrategyService;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
import com.wanshifu.master.order.push.service.common.BackendCommonService;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AgentDistributeStrategyServiceImpl implements AgentDistributeStrategyService {

    @Resource
    private AgentDistributeStrategyApi agentDistributeStrategyApi;

    @Resource
    private BackendCommonService backendCommonService;

    @Resource
    private AddressCommonService addressCommonService;

//    @Resource
//    private AuthHandler authHandler;

    @Resource
    private IopAccountApi iopAccountApi;


    @Override
    public Integer add(AddAgentDistributeStrategyRqt rqt){
//        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        Set<Long> serveIdSet = Arrays.stream(Optional.ofNullable(rqt.getServeIds())
                .orElse("0").split(",")).map(Long::parseLong)
                .collect(Collectors.toSet());

        GetServeByServeIdsReq getServeByServeIdsReq = new GetServeByServeIdsReq();
        getServeByServeIdsReq.setServeIds(serveIdSet);
        List<GetServeByServeIdsResp> respList = backendCommonService.getServeByServeIds(getServeByServeIdsReq);

        List<String> serveNameList = respList.stream().map(GetServeByServeIdsResp::getServeName).collect(Collectors.toList());

        rqt.setServeNames(StringUtils.join(serveNameList,","));

        return agentDistributeStrategyApi.add(rqt);
    }

    @Override
    public Integer update(UpdateAgentDistributeStrategyRqt rqt){
//        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        Set<Long> serveIdSet = Arrays.stream(Optional.ofNullable(rqt.getServeIds())
                .orElse("0").split(",")).map(Long::parseLong)
                .collect(Collectors.toSet());

        GetServeByServeIdsReq getServeByServeIdsReq = new GetServeByServeIdsReq();
        getServeByServeIdsReq.setServeIds(serveIdSet);
        List<GetServeByServeIdsResp> respList = backendCommonService.getServeByServeIds(getServeByServeIdsReq);

        List<String> serveNameList = respList.stream().map(GetServeByServeIdsResp::getServeName).collect(Collectors.toList());

        rqt.setServeNames(StringUtils.join(serveNameList,","));
        return agentDistributeStrategyApi.update(rqt);
    }

    @Override
    public Integer enable(EnableAgentDistributeStrategyRqt rqt){
//        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return agentDistributeStrategyApi.enable(rqt);
    }

    @Override
    public Integer delete(DeleteAgentDistributeStrategyRqt rqt){
//        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return agentDistributeStrategyApi.delete(rqt);
    }

    @Override
    public AgentDistributeStrategyDetailResp detail(AgentDistributeStrategyDetailRqt rqt){
        AgentDistributeDetailResp agentDistributeDetailResp = agentDistributeStrategyApi.detail(rqt);
        if(agentDistributeDetailResp == null){
            return null;
        }

        AgentDistributeStrategyDetailResp resp = new AgentDistributeStrategyDetailResp();
        BeanUtils.copyProperties(agentDistributeDetailResp,resp);

        resp.setAgentName(agentDistributeDetailResp.getAgentInfo().getAgentName());

        Set<Long> serveIdSet = Arrays.stream(Optional.ofNullable(agentDistributeDetailResp.getAgentInfo().getServeIds())
                .orElse("0").split(",")).map(Long::parseLong)
                .collect(Collectors.toSet());

        GetServeByServeIdsReq getServeByServeIdsReq = new GetServeByServeIdsReq();
        getServeByServeIdsReq.setServeIds(serveIdSet);
        List<GetServeByServeIdsResp> respList = backendCommonService.getServeByServeIds(getServeByServeIdsReq);
        List<AgentDistributeStrategyDetailResp.AgentServe> agentServeList = new ArrayList<>();
        respList.forEach(getServeByServeIdsResp -> {
            AgentDistributeStrategyDetailResp.AgentServe agentServe = new AgentDistributeStrategyDetailResp.AgentServe();
            BeanUtils.copyProperties(getServeByServeIdsResp,agentServe);
            agentServeList.add(agentServe);
        });


        List<Address> addressList = addressCommonService.getDivisionInfoListByDivisionIds(agentDistributeDetailResp.getAgentInfo().getDivisionId());

        List<AgentDistributeStrategyDetailResp.AgentDivision> agentDivisionList = new ArrayList<>();
        addressList.forEach(address -> {
            AgentDistributeStrategyDetailResp.AgentDivision agentDivision = new AgentDistributeStrategyDetailResp.AgentDivision();
            agentDivision.setThirdDivisionId(address.getDivisionId());
            agentDivision.setThirdDivisionName(address.getDivisionName());
            agentDivisionList.add(agentDivision);
        });


        Address address = addressList.get(0);
        resp.setProvinceDivisionId(address.getLv2DivisionId());
        resp.setProvince(address.getLv2DivisionName());

        if(address.getLv3DivisionId() != null && address.getLv3DivisionId() > 0){
            resp.setCityDivisionId(address.getLv3DivisionId());
            resp.setCity(address.getLv3DivisionName());
        }else{
            resp.setCityDivisionId(address.getLv4DivisionId());
            resp.setCity(address.getLv4DivisionName());
        }

        resp.setServeList(agentServeList);
        resp.setDivisionList(agentDivisionList);

        return resp;
    }

    @Override
    public SimplePageInfo<GetAgentListResp> getAgentList(GetAgentListRqt rqt){
        SimplePageInfo<AgentInfo> agentInfoSimplePage = agentDistributeStrategyApi.getAgentList(rqt);
        SimplePageInfo<GetAgentListResp> simplePageInfo = new SimplePageInfo<>();
        simplePageInfo.setPageNum(agentInfoSimplePage.getPageNum());
        simplePageInfo.setPageSize(agentInfoSimplePage.getPageSize());
        simplePageInfo.setPages(agentInfoSimplePage.getPages());
        simplePageInfo.setTotal(agentInfoSimplePage.getTotal());
        List<AgentInfo> agentInfoList = agentInfoSimplePage.getList();
        List<GetAgentListResp> getAgentListRespList = new ArrayList<>();
        agentInfoList.forEach(agentInfo -> {
            GetAgentListResp getAgentListResp = new GetAgentListResp();
            BeanUtils.copyProperties(agentInfo, getAgentListResp);
            getAgentListResp.setAgentStatus(agentInfo.getUseStatus());
            Set<Long> serveIdSet = Arrays.stream(Optional.ofNullable(agentInfo.getServeIds())
                    .orElse("0").split(",")).map(Long::parseLong)
                    .collect(Collectors.toSet());

            GetServeByServeIdsReq getServeByServeIdsReq = new GetServeByServeIdsReq();
            getServeByServeIdsReq.setServeIds(serveIdSet);
            List<GetServeByServeIdsResp> respList = backendCommonService.getServeByServeIds(getServeByServeIdsReq);
            List<GetAgentListResp.AgentServe> agentServeList = new ArrayList<>();
            respList.forEach(getServeByServeIdsResp -> {
                GetAgentListResp.AgentServe agentServe = new GetAgentListResp.AgentServe();
                BeanUtils.copyProperties(getServeByServeIdsResp,agentServe);
                agentServeList.add(agentServe);
            });


            List<Address> addressList = addressCommonService.getDivisionInfoListByDivisionIds(agentInfo.getDivisionId());

            List<GetAgentListResp.AgentDivision> agentDivisionList = new ArrayList<>();
            addressList.forEach(address -> {
                GetAgentListResp.AgentDivision agentDivision = new GetAgentListResp.AgentDivision();
                agentDivision.setThirdDivisionId(address.getDivisionId());
                agentDivision.setThirdDivisionName(address.getDivisionName());
                agentDivisionList.add(agentDivision);
            });


            Address address = addressList.get(0);
            getAgentListResp.setProvinceDivisionId(address.getLv2DivisionId());
            getAgentListResp.setProvince(address.getLv2DivisionName());

            if(address.getLv3DivisionId() != null && address.getLv3DivisionId() > 0){
                getAgentListResp.setCityDivisionId(address.getLv3DivisionId());
                getAgentListResp.setCity(address.getLv3DivisionName());
            }else{
                getAgentListResp.setCityDivisionId(address.getLv4DivisionId());
                getAgentListResp.setCity(address.getLv4DivisionName());
            }

            List<String> divisionNameList = addressList.stream().map(Address::getDivisionName).collect(Collectors.toList());

            getAgentListResp.setDivisionNames(StringUtils.join(divisionNameList,","));


            getAgentListResp.setServeList(agentServeList);
            getAgentListResp.setDivisionList(agentDivisionList);

            getAgentListRespList.add(getAgentListResp);
        });

        simplePageInfo.setList(getAgentListRespList);

        return simplePageInfo;

    }

    @Override
    public SimplePageInfo<GetAgentDistributeStrategyListResp> list(GetAgentDistributeStrategyListRqt rqt){
        SimplePageInfo<AgentDistributeStrategy> agentDistributeSimplePageInfo = agentDistributeStrategyApi.list(rqt);

        SimplePageInfo<GetAgentDistributeStrategyListResp> simplePageInfo = new SimplePageInfo<>();
        simplePageInfo.setPageNum(agentDistributeSimplePageInfo.getPageNum());
        simplePageInfo.setPageSize(agentDistributeSimplePageInfo.getPageSize());
        simplePageInfo.setPages(agentDistributeSimplePageInfo.getPages());
        simplePageInfo.setTotal(agentDistributeSimplePageInfo.getTotal());

        List<GetAgentDistributeStrategyListResp> getAgentDistributeStrategyListRespList = new ArrayList<>();
        List<AgentDistributeStrategy> agentDistributeStrategyList = agentDistributeSimplePageInfo.getList();

        agentDistributeStrategyList.forEach(agentDistributeStrategy -> {
            GetAgentDistributeStrategyListResp getAgentDistributeStrategyListResp = new GetAgentDistributeStrategyListResp();
            getAgentDistributeStrategyListResp.setStrategyId(agentDistributeStrategy.getStrategyId());
            getAgentDistributeStrategyListResp.setMasterSourceType(agentDistributeStrategy.getMasterSourceType());

            List<Address> addressList = addressCommonService.getDivisionInfoListByDivisionIds(agentDistributeStrategy.getThirdDivisionIds());

            Address address = addressList.get(0);
            getAgentDistributeStrategyListResp.setProvince(address.getLv2DivisionName());

            if(address.getLv3DivisionId() != null && address.getLv3DivisionId() > 0){
                getAgentDistributeStrategyListResp.setCity(address.getLv3DivisionName());
            }else{
                getAgentDistributeStrategyListResp.setCity(address.getLv4DivisionName());
            }



            List<String> divisionNameList = addressList.stream().map(Address::getDivisionName).collect(Collectors.toList());

            getAgentDistributeStrategyListResp.setDistrict(StringUtils.join(divisionNameList,","));


            getAgentDistributeStrategyListResp.setStrategyName(agentDistributeStrategy.getStrategyName());

            getAgentDistributeStrategyListResp.setDistributeRule(AgentDistributeRule.DIRECT_APPOINT.code.equals(agentDistributeStrategy.getDistributeRule()) ? "直接指派" : "定向推送");
            getAgentDistributeStrategyListResp.setAgentName(agentDistributeStrategy.getAgentName());
            getAgentDistributeStrategyListResp.setStrategyStatus(agentDistributeStrategy.getStrategyStatus());

            getAgentDistributeStrategyListResp.setServe(agentDistributeStrategy.getServeNames());

            List<Long> updateAccountIds = agentDistributeStrategyList.stream().map(AgentDistributeStrategy::getUpdateAccountId)
                    .distinct().filter(Objects::nonNull)
                    .collect(Collectors.toList());
            Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(updateAccountIds)) {
                iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                        .map(IopAccountResp::getRetData).orElse(Collections.emptyList()).stream()
                        .collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
            }
            Map<Long, IopAccountResp.IopAccount> finalIopAccountMap = iopAccountMap;

            getAgentDistributeStrategyListResp.setLastUpdateAccountName(Optional.ofNullable(finalIopAccountMap.get(agentDistributeStrategy.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));

            getAgentDistributeStrategyListResp.setCreateTime(agentDistributeStrategy.getCreateTime());
            getAgentDistributeStrategyListResp.setUpdateTime(agentDistributeStrategy.getUpdateTime());
            getAgentDistributeStrategyListRespList.add(getAgentDistributeStrategyListResp);
        });


        simplePageInfo.setList(getAgentDistributeStrategyListRespList);
        return simplePageInfo;
    }


}
