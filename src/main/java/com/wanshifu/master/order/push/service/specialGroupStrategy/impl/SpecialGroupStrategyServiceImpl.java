package com.wanshifu.master.order.push.service.specialGroupStrategy.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.api.SpecialGroupStrategyApi;
import com.wanshifu.master.order.push.api.iop.IopAccountApi;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import com.wanshifu.master.order.push.domain.request.common.IopGetInfoListByAccountIdsReq;
import com.wanshifu.master.order.push.domain.response.common.IopAccountResp;
import com.wanshifu.master.order.push.domain.response.specialGroup.DetailResp;
import com.wanshifu.master.order.push.domain.response.specialGroup.ListResp;
import com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy.*;
import com.wanshifu.master.order.push.service.common.AddressCommonService;
import com.wanshifu.master.order.push.service.common.ServeCommonService;
import com.wanshifu.master.order.push.service.specialGroupStrategy.SpecialGroupStrategyService;
import com.wanshifu.master.order.push.util.JsonValueUtils;
import com.wanshifu.master.order.push.util.UserInfoUtils;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SpecialGroupStrategyServiceImpl implements SpecialGroupStrategyService {

    @Resource
    private SpecialGroupStrategyApi specialGroupStrategyApi;
    @Resource
    private ServeCommonService serveCommonService;
    @Resource
    private IopAccountApi iopAccountApi;
    @Resource
    private AddressCommonService addressCommonService;

    @Override
    public int create(CreateRqt rqt) {
        rqt.setCreateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return specialGroupStrategyApi.create(rqt);
    }

    @Override
    public int update(UpdateRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return specialGroupStrategyApi.update(rqt);
    }

    @Override
    public DetailResp detail(DetailRqt rqt) {
        DetailResp resp = new DetailResp();
        SpecialGroupStrategy strategy = specialGroupStrategyApi.detail(rqt);
        if (strategy == null) {
            return null;
        }
        BeanUtils.copyProperties(strategy, resp);
        String serveIdArray = strategy.getServeIdArray();
        resp.setServeIds(serveIdArray);

        Set<Long> serveIdSet = JSON.parseObject(serveIdArray, new TypeReference<List<List<Long>>>(){})
                .stream().flatMap(List::stream).collect(Collectors.toSet());
        List<ServeBaseInfoResp> serveList = serveCommonService.getServeBaseInfoByServeIdSet(serveIdSet);
        if (CollectionUtils.isNotEmpty(serveList)) {
            List<String> serviceInfoList = new ArrayList<>();
            for (ServeBaseInfoResp serveResp : serveList) {
                String serveIdAndName = serveResp.getServeId() + ":" + serveResp.getName();
                serviceInfoList.add(serveIdAndName);
            }
            resp.setServiceInfoList(serviceInfoList);
        }
        return resp;
    }

    @Override
    public SimplePageInfo<ListResp> list(ListRqt rqt) {
        SimplePageInfo<SpecialGroupStrategy> pages = specialGroupStrategyApi.list(rqt);
        if (pages == null) {
            return new SimplePageInfo<>();
        }
        List<SpecialGroupStrategy> strategies = pages.getList();
        if (CollectionUtils.isEmpty(strategies)) {
            return new SimplePageInfo<>();
        }
        List<Long> updateAccountIds = strategies.stream().map(SpecialGroupStrategy::getUpdateAccountId)
                .distinct().filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, IopAccountResp.IopAccount> iopAccountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(updateAccountIds)) {
            iopAccountMap = Optional.ofNullable(iopAccountApi.getInfoListByAccountIds(new IopGetInfoListByAccountIdsReq(updateAccountIds)))
                    .map(IopAccountResp::getRetData).orElse(Collections.emptyList())
                    .stream().collect(Collectors.toMap(IopAccountResp.IopAccount::getAccountId, Function.identity()));
        }
        List<ListResp> respList = new ArrayList<>();
        for (SpecialGroupStrategy strategy : strategies) {
            ListResp resp = new ListResp();
            respList.add(resp);
            BeanUtils.copyProperties(strategy, resp);

            resp.setLastUpdateAccountName(Optional.ofNullable(iopAccountMap.get(strategy.getUpdateAccountId())).map(IopAccountResp.IopAccount::getChineseName).orElse(""));

            List<ServeBaseInfoResp> serveBaseInfoList = serveCommonService.getServeBaseInfoByServeIdSet(Arrays.stream(strategy.getServeIds().split(","))
                    .filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toSet()));

            Optional.ofNullable(serveBaseInfoList).ifPresent(serveBaseInfoRespList -> {
                List<String> serveNameList = serveBaseInfoRespList.stream().map(ServeBaseInfoResp::getName).collect(Collectors.toList());
                resp.setServeNames(String.join(",", serveNameList));
            });

            List<Address> addressList = addressCommonService.getDivisionInfoListByDivisionIds(strategy.getCityIds());
            Optional.ofNullable(addressList).ifPresent(addressList1 -> {
                List<String> cityNameList = addressList1.stream().map(Address::getDivisionName).collect(Collectors.toList());
                resp.setCityNames(String.join(",", cityNameList));
            });
            resp.setPushGroups(JsonValueUtils.extractAllValues(strategy.getPushGroups()));
        }
        SimplePageInfo<ListResp> resultPage = new SimplePageInfo<>();
        resultPage.setPageNum(pages.getPageNum());
        resultPage.setPageSize(pages.getPageSize());
        resultPage.setPages(pages.getPages());
        resultPage.setTotal(pages.getTotal());
        resultPage.setList(respList);
        return resultPage;
    }

    @Override
    public Integer enable(EnableRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return specialGroupStrategyApi.enable(rqt);
    }

    @Override
    public Integer delete(DeleteRqt rqt) {
        rqt.setUpdateAccountId(UserInfoUtils.getCurrentLoginAccountId());
        return specialGroupStrategyApi.delete(rqt);
    }
}
