package com.wanshifu.master.order.push.controller.cron;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/cron")
@Slf4j
public class CronController {

    private static final String GET_ENDPOINTS_URL = "http://yw-assembly-line.wanshifu.com/api/online-manage-application/get_end_points/";
    private static final String CHANGE_LOG_LEVEL_URL = "http://yw-assembly-line.wanshifu.com/api/online-manage-application/change_log_level/";
    private static final String AUTHORIZATION_TOKEN = "JWT eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************.SlqEH7nBiyM2V2V2yUoCCY3FGPrq8q3_f2gzVNmsUws";
    private static final String APP_NAME = "master-order-push-service";

    /**
     * 定时任务接口：调整日志级别
     */
    @Scheduled(cron = "0 03 * * * ?")
    @GetMapping("/changeLogLevel")
    public void changeLogLevel() {
        final String level = "INFO";
        try {
            log.info("开始执行日志级别调整任务，目标级别：{}", level);

            // 第一步：获取端点信息
            JSONArray endpoints = getEndpoints();
            if (endpoints == null || endpoints.isEmpty()) {
                return;
            }

            log.info("成功获取到 {} 个端点信息", endpoints.size());

            // 第二步：调用日志级别调整接口
            boolean changeResult = changeLogLevel(level, endpoints);

            if (changeResult) {
                log.info("日志级别调整任务执行成功，级别：{}，影响端点数：{}", level, endpoints.size());
            } else {
                log.error("日志级别调整任务执行失败，级别：{}，影响端点数：{}", level, endpoints.size());
            }

        } catch (Exception e) {
            log.error("执行日志级别调整任务时发生异常", e);
        }
    }

    /**
     * 获取端点信息
     * @return 端点信息数组
     */
    private JSONArray getEndpoints() {
        try {
            log.info("开始获取端点信息，URL：{}", GET_ENDPOINTS_URL);

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("app_name", APP_NAME);

            // 发送GET请求
            HttpResponse response = HttpUtil.createGet(GET_ENDPOINTS_URL)
                    .form(params)
                    .timeout(10000)
                    .execute();

            if (!response.isOk()) {
                log.error("获取端点信息请求失败，状态码：{}", response.getStatus());
                return null;
            }

            String responseBody = response.body();
            log.info("获取端点信息响应：{}", responseBody);

            JSONObject responseJson = JSONUtil.parseObj(responseBody);

            // 检查响应状态
            if (responseJson.getInt("status") != 200) {
                log.error("获取端点信息失败，响应状态：{}, 消息：{}",
                         responseJson.getInt("status"), responseJson.getStr("msg"));
                return null;
            }

            return responseJson.getJSONArray("data");

        } catch (Exception e) {
            log.error("获取端点信息时发生异常", e);
            return null;
        }
    }

    /**
     * 调用日志级别调整接口
     * @param level 日志级别
     * @param endpoints 端点信息
     * @return 是否成功
     */
    private boolean changeLogLevel(String level, JSONArray endpoints) {
        try {
            log.info("开始调用日志级别调整接口，级别：{}，端点数：{}", level, endpoints.size());

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("level", level);
            requestBody.put("points", endpoints);
            requestBody.put("app_name", APP_NAME);

            log.info("日志级别调整请求体：{}", requestBody);

            // 发送POST请求
            HttpResponse response = HttpUtil.createPost(CHANGE_LOG_LEVEL_URL)
                    .header("Authorization", AUTHORIZATION_TOKEN)
                    .header("Content-Type", "application/json")
                    .body(requestBody.toString())
                    .timeout(60000)
                    .execute();

            if (!response.isOk()) {
                log.error("日志级别调整请求失败，状态码：{}", response.getStatus());
                return false;
            }

            String responseBody = response.body();
            log.info("日志级别调整响应：{}", responseBody);

            // 这里可以根据实际的响应格式来判断是否成功
            // 假设成功的响应包含success字段或者状态码为200
            return true;

        } catch (Exception e) {
            log.error("调用日志级别调整接口时发生异常", e);
            return false;
        }
    }
}
