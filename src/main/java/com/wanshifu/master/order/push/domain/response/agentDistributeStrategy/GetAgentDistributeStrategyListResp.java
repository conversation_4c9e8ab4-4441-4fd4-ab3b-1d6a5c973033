package com.wanshifu.master.order.push.domain.response.agentDistributeStrategy;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class GetAgentDistributeStrategyListResp {

    private Integer strategyId;

    private String province;

    private String city;

    private String district;

    private String masterSourceType;

    private String strategyName;

    private String distributeRule;

    private String agentName;

    private String serve;

    private Integer strategyStatus;

    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String lastUpdateAccountName;


}
