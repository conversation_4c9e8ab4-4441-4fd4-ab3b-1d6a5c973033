package com.wanshifu.master.order.push.service.agentDistributeStrategy;

import com.wanshifu.framework.core.page.SimplePageInfo;

import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.AgentDistributeStrategyDetailResp;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.GetAgentDistributeStrategyListResp;
import com.wanshifu.master.order.push.domain.response.agentDistributeStrategy.GetAgentListResp;
import com.wanshifu.master.order.push.domain.rqt.agent.*;

public interface AgentDistributeStrategyService {

    Integer add(AddAgentDistributeStrategyRqt rqt);

    Integer update(UpdateAgentDistributeStrategyRqt rqt);

    Integer enable(EnableAgentDistributeStrategyRqt rqt);

    Integer delete(DeleteAgentDistributeStrategyRqt rqt);

    AgentDistributeStrategyDetailResp detail(AgentDistributeStrategyDetailRqt rqt);

    SimplePageInfo<GetAgentListResp> getAgentList(GetAgentListRqt rqt);

    SimplePageInfo<GetAgentDistributeStrategyListResp> list(GetAgentDistributeStrategyListRqt rqt);

}
